<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationForgetPassword, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import {
  getEmailByUserNameApi,
  getMobileByUserNameApi,
  resetPasswordApi,
  sendEmailCodeApi,
  sendSmsCodeApi,
} from '#/api';

defineOptions({ name: 'ForgetPassword' });

const router = useRouter();
const loading = ref(false);
const active = ref<1 | 2 | 3>(3);
interface StepConfig {
  [key: number]: VbenFormSchema[]; // 明确表示可以用数字索引
}
const confirmAccount = ref('');
const findType = ref('');
const userName = ref('');
const captcha = ref('');
const formSchema = computed((): VbenFormSchema[] => {
  const step: StepConfig = {
    1: [
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入找回账号',
        },
        fieldName: 'userName',
        label: $t('authentication.username'),
        rules: z.string().min(1, { message: $t('authentication.userNameTip') }),
      },
      {
        component: 'VbenSelect',
        componentProps: {
          placeholder: '请选择找回方式',
          options: [
            { label: '通过邮箱找回', value: 'email' },
            { label: '通过手机找回', value: 'mobile' },
          ],
        },
        fieldName: 'type',
        label: '找回方式',
        rules: z.string().min(1, { message: '请选择找回方式' }),
      },
    ],
    2: [
      {
        component: 'VbenInput',
        componentProps: {
          disabled: true,
          modelValue: confirmAccount.value,
        },
        fieldName: 'showAccount',
        label: '确认账号',
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请确认邮箱/手机号',
        },
        fieldName: 'confirmAccount',
        label: '确认账号',
        rules: z.string().min(1, { message: '请输入请确认邮箱/手机号' }),
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入验证码',
        },
        fieldName: 'captcha',
        label: '验证码',
        rules: z.string().min(1, { message: '请输入验证码' }),
      },
    ],
    3: [
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入新密码',
          type: 'password',
        },
        fieldName: 'newPassword',
        label: '新密码',
        rules: z.string().min(1, { message: '请输入新密码' }),
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请确认新密码',
          type: 'password',
        },
        dependencies: {
          rules(values) {
            const { newPassword } = values;
            return z
              .string({ required_error: '请输入确认新密码' })
              .min(1, { message: '请输入确认新密码' })
              .refine((value) => value === newPassword, {
                message: '两次输入的密码不一致',
              });
          },
          triggerFields: ['newPassword'],
        },
        fieldName: 'confirmPassword',
        label: '确认新密码',
      },
    ],
  };
  return step[active.value]!;
});
const AuthForgetPasswordRef = ref();
async function handleSubmit(value: Recordable<any>) {
  // eslint-disable-next-line no-console
  console.log('value:', value);
  switch (active.value) {
    case 1: {
      userName.value = value.userName;
      findType.value = value.type;
      let api = getMobileByUserNameApi;
      if (value.type === 'email') {
        // 通过邮箱找回
        api = getEmailByUserNameApi;
      }
      confirmAccount.value = await api({ userName: value.userName });
      active.value = 2;

      break;
    }
    case 2: {
      captcha.value = value.captcha;
      const sendCodeApi = findType.value === 'mobile' ? sendSmsCodeApi : sendEmailCodeApi;
      await sendCodeApi({
        userName: userName.value,
        [findType.value === 'mobile' ? 'mobile' : 'email']: value.confirmAccount,
      });

      active.value = 3;
      break;
    }
    case 3: {
      const params = {
        captcha: value.captcha,
        newPassword: value.newPassword,
        userName: userName.value,
      };
      await resetPasswordApi(params);
      await router.push({name: 'Login'});
      break;
    }
    // No default
  }
}
</script>

<template>
  <AuthenticationForgetPassword
    ref="AuthForgetPasswordRef"
    :form-schema="formSchema"
    :loading="loading"
    @submit="handleSubmit"
  />
</template>
